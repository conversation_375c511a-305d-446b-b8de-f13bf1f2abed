"use client";

import React from "react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import EnhancedAuctionForm from "@/modules/dashboard/components/vendor/EnhancedAuctionForm";
import VerificationGuard from "@/modules/dashboard/components/shared/VerificationGuard";

export default function CreateEnhancedAuctionPage() {
  const router = useRouter();

  const handleSuccess = () => {
    router.push("/dashboard/vendor/auctions");
  };

  const handleCancel = () => {
    router.back();
  };

  return (
    <DashboardLayout>
      <VerificationGuard requiredRole="vendor">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Create Enhanced Auction
              </h1>
              <p className="text-gray-600 mt-2">
                Create an auction with multiple items and advanced features
              </p>
            </div>
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 focus:outline-none"
            >
              ← Back
            </button>
          </div>
        </div>

        <EnhancedAuctionForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </VerificationGuard>
    </DashboardLayout>
  );
}
