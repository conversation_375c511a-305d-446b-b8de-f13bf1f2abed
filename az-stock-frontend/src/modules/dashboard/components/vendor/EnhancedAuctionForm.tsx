"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import VendorService from "../../services/VendorService";
import {
  CreateAuctionForm,
  CreateAuctionItemForm,
  CategoryModel,
} from "../../models/DashboardModels";
import ImageUpload from "../shared/ImageUpload";

const vendorService = new VendorService();

interface EnhancedAuctionFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function EnhancedAuctionForm({
  onSuccess,
  onCancel,
}: EnhancedAuctionFormProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<CategoryModel[]>([]);

  const [formData, setFormData] = useState<CreateAuctionForm>({
    description: "",
    auction_type: "online",
    featured_image: "",
    start_time: "",
    end_time: "",
    starting_price: 0,
    reserve_price: 0,
    auction_items: [
      {
        name: "",
        description: "",
        category_id: 0,
        image: "",
        estimated_value: 0,
        quantity: 1,
      },
    ],
  });

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      const response = await vendorService.methods.getCategories();
      setCategories(response.data);
    } catch (error) {
      console.error("Error loading categories:", error);
      toast.error("Failed to load categories");
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name.includes("price") ? parseFloat(value) || 0 : value,
    }));
  };

  const handleItemChange = (
    index: number,
    field: keyof CreateAuctionItemForm,
    value: string | number
  ) => {
    setFormData((prev) => ({
      ...prev,
      auction_items: prev.auction_items.map((item, i) =>
        i === index
          ? {
              ...item,
              [field]:
                field === "category_id" ||
                field === "estimated_value" ||
                field === "quantity"
                  ? parseInt(value as string) || 0
                  : value,
            }
          : item
      ),
    }));
  };

  const addAuctionItem = () => {
    setFormData((prev) => ({
      ...prev,
      auction_items: [
        ...prev.auction_items,
        {
          name: "",
          description: "",
          category_id: 0,
          image: "",
          estimated_value: 0,
          quantity: 1,
        },
      ],
    }));
  };

  const removeAuctionItem = (index: number) => {
    if (formData.auction_items.length > 1) {
      setFormData((prev) => ({
        ...prev,
        auction_items: prev.auction_items.filter((_, i) => i !== index),
      }));
    }
  };

  const handleFeaturedImageUpload = (imageUrl: string) => {
    setFormData((prev) => ({
      ...prev,
      featured_image: imageUrl,
    }));
  };

  const handleItemImageUpload = (index: number, imageUrl: string) => {
    handleItemChange(index, "image", imageUrl);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.start_time || !formData.end_time) {
      toast.error("Please set auction start and end times");
      return;
    }

    if (formData.starting_price <= 0) {
      toast.error("Starting price must be greater than 0");
      return;
    }

    if (
      formData.auction_items.some((item) => !item.name || !item.category_id)
    ) {
      toast.error("Please fill in all required fields for auction items");
      return;
    }

    setLoading(true);
    try {
      await vendorService.methods.createAuctionWithItems(formData);
      toast.success("Auction created successfully!");

      if (onSuccess) {
        onSuccess();
      } else {
        router.push("/dashboard/vendor/auctions");
      }
    } catch (error: any) {
      console.error("Error creating auction:", error);
      toast.error(error.message || "Failed to create auction");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Create New Auction</h2>
        <p className="text-gray-600 mt-2">
          Create an auction with multiple items and set your preferences
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Auction Details Section */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Auction Details
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Auction Type *
              </label>
              <select
                name="auction_type"
                value={formData.auction_type}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              >
                <option value="online">Online (Transparent Bidding)</option>
                <option value="sealed">Sealed (Private Bidding)</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                {formData.auction_type === "online"
                  ? "All bids are visible to everyone"
                  : "Bids are private - only you and the bidder can see them"}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Featured Image
              </label>
              <ImageUpload
                onImageUpload={handleFeaturedImageUpload}
                currentImage={formData.featured_image}
                className="w-full"
              />
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Auction Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="Describe your auction, terms, and conditions..."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Time *
              </label>
              <input
                type="datetime-local"
                name="start_time"
                value={formData.start_time}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                End Time *
              </label>
              <input
                type="datetime-local"
                name="end_time"
                value={formData.end_time}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Starting Price ($) *
              </label>
              <input
                type="number"
                name="starting_price"
                value={formData.starting_price}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reserve Price ($)
              </label>
              <input
                type="number"
                name="reserve_price"
                value={formData.reserve_price}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                Minimum price to accept (optional)
              </p>
            </div>
          </div>
        </div>

        {/* Auction Items Section */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Auction Items ({formData.auction_items.length})
            </h3>
            <button
              type="button"
              onClick={addAuctionItem}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              Add Item
            </button>
          </div>

          <div className="space-y-6">
            {formData.auction_items.map((item, index) => (
              <div key={index} className="bg-white p-6 rounded-lg border">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-md font-medium text-gray-900">
                    Item #{index + 1}
                  </h4>
                  {formData.auction_items.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeAuctionItem(index)}
                      className="text-red-600 hover:text-red-800 focus:outline-none"
                    >
                      Remove
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Item Name *
                    </label>
                    <input
                      type="text"
                      value={item.name}
                      onChange={(e) =>
                        handleItemChange(index, "name", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      placeholder="Enter item name"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Category *
                    </label>
                    <select
                      value={item.category_id}
                      onChange={(e) =>
                        handleItemChange(index, "category_id", e.target.value)
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      required
                    >
                      <option value="">Select Category</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Item Description
                  </label>
                  <textarea
                    value={item.description}
                    onChange={(e) =>
                      handleItemChange(index, "description", e.target.value)
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    placeholder="Describe the item..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Estimated Value ($)
                    </label>
                    <input
                      type="number"
                      value={item.estimated_value}
                      onChange={(e) =>
                        handleItemChange(
                          index,
                          "estimated_value",
                          e.target.value
                        )
                      }
                      min="0"
                      step="0.01"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Quantity *
                    </label>
                    <input
                      type="number"
                      value={item.quantity}
                      onChange={(e) =>
                        handleItemChange(index, "quantity", e.target.value)
                      }
                      min="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Item Image
                    </label>
                    <ImageUpload
                      onImageUpload={(imageUrl) =>
                        handleItemImageUpload(index, imageUrl)
                      }
                      currentImage={item.image}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? "Creating..." : "Create Auction"}
          </button>
        </div>
      </form>
    </div>
  );
}
