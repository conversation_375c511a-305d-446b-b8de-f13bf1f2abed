<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuctionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $user = $request->user();
        $userId = $user ? $user->id : null;

        return [
            'id' => $this->id,
            'description' => $this->description,
            'auction_type' => $this->auction_type,
            'featured_image' => $this->featured_image,
            'featured_image_url' => $this->featured_image_url,
            'start_time' => $this->start_time,
            'end_time' => $this->end_time,
            'starting_price' => $this->starting_price,
            'current_price' => $this->current_price,
            'reserve_price' => $this->reserve_price,
            'status' => $this->status,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,

            // New auction items relationship
            'auction_items' => $this->when(
                $this->relationLoaded('auctionItems'),
                SimpleProductResource::collection($this->auctionItems)
            ),

            // Keep backward compatibility with product
            'product' => $this->when(
                $this->relationLoaded('auctionItems') && $this->auctionItems->isNotEmpty(),
                new SimpleProductResource($this->auctionItems->first())
            ),

            'vendor' => $this->when($this->relationLoaded('vendor'), new SimpleVendorResource($this->vendor)),

            // Handle bid visibility based on auction type
            'bids' => $this->when($this->relationLoaded('bids'), function() use ($userId) {
                if ($this->isSealed()) {
                    // For sealed auctions, only show user's own bids
                    $visibleBids = $userId ? $this->bids->where('user_id', $userId) : collect();
                    return SimpleBidResource::collection($visibleBids);
                }
                // For online auctions, show all bids
                return SimpleBidResource::collection($this->bids);
            }),

            'bids_count' => $this->when(isset($this->bids_count), $this->bids_count),
            'user_bid_count' => $this->when($userId && $this->relationLoaded('bids'),
                $this->bids->where('user_id', $userId)->count()
            ),

            'time_remaining' => $this->when(
                $this->end_time && $this->end_time > now(),
                $this->end_time ? $this->end_time->diffForHumans() : null
            ),
            'is_active' => $this->status === 'active' &&
                           ($this->start_time ? $this->start_time <= now() : true) &&
                           ($this->end_time ? $this->end_time > now() : true),
        ];
    }
}
