<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AuctionItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'auction_id' => $this->auction_id,
            'name' => $this->name,
            'description' => $this->description,
            'image' => $this->image,
            'image_url' => $this->getImageUrl(),
            'estimated_value' => $this->estimated_value,
            'quantity' => $this->quantity,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'category' => $this->when($this->relationLoaded('category'), new SimpleCategoryResource($this->category)),
            'vendor' => $this->when($this->relationLoaded('vendor'), new SimpleVendorResource($this->vendor)),
            'images' => $this->when($this->relationLoaded('images'), ProductImageResource::collection($this->images)),
        ];
    }
}
