<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'slug', 'email', 'logo', 'website', 'phone', 'address', 'description', 'is_active', 'user_id',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the logo URL (for API responses)
     *
     * @return string|null
     */
    public function getLogoUrl(): ?string
    {
        if (!$this->logo) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->logo, FILTER_VALIDATE_URL)) {
            return $this->logo;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->logo);
    }

    /**
     * Get profile completion percentage
     *
     * @return int
     */
    public function getProfileCompletionPercentage(): int
    {
        $fields = [
            'name' => !empty($this->name),
            'email' => !empty($this->email),
            'description' => !empty($this->description),
            'website' => !empty($this->website),
            'phone' => !empty($this->phone),
            'address' => !empty($this->address),
            'logo' => !empty($this->logo),
        ];

        $completedFields = array_filter($fields);
        return (int) round((count($completedFields) / count($fields)) * 100);
    }

    /**
     * Check if profile is complete
     *
     * @return bool
     */
    public function isProfileComplete(): bool
    {
        return $this->getProfileCompletionPercentage() >= 80; // 80% completion threshold
    }

    /**
     * Get missing profile fields
     *
     * @return array
     */
    public function getMissingProfileFields(): array
    {
        $fields = [
            'name' => 'Business Name',
            'email' => 'Contact Email',
            'description' => 'Business Description',
            'website' => 'Website URL',
            'phone' => 'Phone Number',
            'address' => 'Business Address',
            'logo' => 'Business Logo',
        ];

        $missing = [];
        foreach ($fields as $field => $label) {
            if (empty($this->$field)) {
                $missing[$field] = $label;
            }
        }

        return $missing;
    }
}
