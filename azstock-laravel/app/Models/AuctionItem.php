<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AuctionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'auction_id',
        'vendor_id',
        'name',
        'description',
        'category_id',
        'image',
        'estimated_value',
        'quantity'
    ];

    protected $casts = [
        'estimated_value' => 'decimal:2',
        'quantity' => 'integer'
    ];

    public function auction()
    {
        return $this->belongsTo(Auction::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'product_id'); // Keep compatibility with existing reviews
    }

    /**
     * Get the image path attribute for backward compatibility
     */
    public function getImagePathAttribute()
    {
        if (!$this->image) {
            return asset('images/default-product.jpg');
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image);
    }

    /**
     * Get the image URL (for API responses)
     *
     * @return string|null
     */
    public function getImageUrl(): ?string
    {
        if (!$this->image) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->image);
    }

    public function images()
    {
        return $this->hasMany(ProductImage::class, 'product_id'); // Keep compatibility with existing images
    }
}
