<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class RenameProductsToAuctionItems extends Migration
{
    public function up()
    {
        // First, add auction_id column to products table
        Schema::table('products', function (Blueprint $table) {
            $table->foreignId('auction_id')->nullable()->constrained('auctions')->onDelete('cascade')->after('vendor_id');
            $table->decimal('estimated_value', 10, 2)->nullable()->after('image');
            $table->integer('quantity')->default(1)->after('estimated_value');
        });

        // Update existing products to be linked to their auctions
        $auctions = DB::table('auctions')->get();
        foreach ($auctions as $auction) {
            DB::table('products')
                ->where('id', $auction->product_id)
                ->update([
                    'auction_id' => $auction->id,
                    'estimated_value' => $auction->starting_price,
                    'quantity' => 1
                ]);
        }

        // Now rename the table
        Schema::rename('products', 'auction_items');

        // Update the auctions table to remove product_id foreign key
        Schema::table('auctions', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
            $table->dropColumn('product_id');
        });
    }

    public function down()
    {
        // Rename back to products
        Schema::rename('auction_items', 'products');

        // Re-add product_id to auctions
        Schema::table('auctions', function (Blueprint $table) {
            $table->foreignId('product_id')->nullable()->constrained('products')->onDelete('cascade');
        });

        // Remove the new columns from products
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['auction_id']);
            $table->dropColumn(['auction_id', 'estimated_value', 'quantity']);
        });

        // Restore the product_id relationships
        $auctions = DB::table('auctions')->get();
        foreach ($auctions as $auction) {
            $product = DB::table('products')->where('auction_id', $auction->id)->first();
            if ($product) {
                DB::table('auctions')
                    ->where('id', $auction->id)
                    ->update(['product_id' => $product->id]);
            }
        }
    }
}
