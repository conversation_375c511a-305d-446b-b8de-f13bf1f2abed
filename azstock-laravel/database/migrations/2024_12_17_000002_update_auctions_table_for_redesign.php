<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateAuctionsTableForRedesign extends Migration
{
    public function up()
    {
        Schema::table('auctions', function (Blueprint $table) {
            // Add new columns if they don't exist (for fresh installations)
            if (!Schema::hasColumn('auctions', 'title')) {
                $table->string('title')->after('vendor_id');
            }
            if (!Schema::hasColumn('auctions', 'description')) {
                $table->text('description')->nullable()->after('title');
            }
            if (!Schema::hasColumn('auctions', 'auction_type')) {
                $table->enum('auction_type', ['online', 'sealed'])->default('online')->after('description');
            }
            if (!Schema::hasColumn('auctions', 'featured_image')) {
                $table->string('featured_image')->nullable()->after('auction_type');
            }
        });

        // Remove product_id foreign key constraint if it exists
        if (Schema::hasColumn('auctions', 'product_id')) {
            Schema::table('auctions', function (Blueprint $table) {
                $table->dropForeign(['product_id']);
                $table->dropColumn('product_id');
            });
        }
    }

    public function down()
    {
        Schema::table('auctions', function (Blueprint $table) {
            // Re-add product_id column
            $table->foreignId('product_id')->nullable()->constrained('products')->onDelete('cascade');
            
            // Remove new columns
            if (Schema::hasColumn('auctions', 'title')) {
                $table->dropColumn('title');
            }
            if (Schema::hasColumn('auctions', 'description')) {
                $table->dropColumn('description');
            }
            if (Schema::hasColumn('auctions', 'auction_type')) {
                $table->dropColumn('auction_type');
            }
            if (Schema::hasColumn('auctions', 'featured_image')) {
                $table->dropColumn('featured_image');
            }
        });
    }
}
