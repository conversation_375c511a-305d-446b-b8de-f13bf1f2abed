<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class MigrateExistingAuctionData extends Migration
{
    public function up()
    {
        // First, update existing auctions with default values for new fields
        DB::table('auctions')->update([
            'title' => DB::raw("CONCAT('Auction #', id)"),
            'description' => null,
            'auction_type' => 'online',
            'featured_image' => null
        ]);

        // Migrate existing auction-product relationships to auction_items
        $existingAuctions = DB::table('auctions')
            ->join('products', 'auctions.product_id', '=', 'products.id')
            ->select(
                'auctions.id as auction_id',
                'auctions.vendor_id',
                'products.id as product_id',
                'products.name',
                'products.description',
                'products.category_id',
                'products.image',
                'auctions.starting_price as estimated_value',
                'auctions.created_at',
                'auctions.updated_at'
            )
            ->get();

        foreach ($existingAuctions as $auction) {
            // Update auction title with product name
            DB::table('auctions')
                ->where('id', $auction->auction_id)
                ->update([
                    'title' => $auction->name,
                    'description' => $auction->description,
                    'featured_image' => $auction->image
                ]);

            // Create auction item
            DB::table('auction_items')->insert([
                'auction_id' => $auction->auction_id,
                'vendor_id' => $auction->vendor_id,
                'name' => $auction->name,
                'description' => $auction->description,
                'category_id' => $auction->category_id,
                'image' => $auction->image,
                'estimated_value' => $auction->estimated_value,
                'quantity' => 1,
                'created_at' => $auction->created_at,
                'updated_at' => $auction->updated_at
            ]);
        }
    }

    public function down()
    {
        // Clear auction_items table
        DB::table('auction_items')->truncate();
        
        // Reset auction titles and descriptions
        DB::table('auctions')->update([
            'title' => DB::raw("CONCAT('Auction #', id)"),
            'description' => null,
            'auction_type' => 'online',
            'featured_image' => null
        ]);
    }
}
