<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class EnhanceAuctionsTable extends Migration
{
    public function up()
    {
        Schema::table('auctions', function (Blueprint $table) {
            // Add new columns for enhanced auction functionality
            $table->text('description')->nullable()->after('vendor_id');
            $table->enum('auction_type', ['online', 'sealed'])->default('online')->after('description');
            $table->string('featured_image')->nullable()->after('auction_type');
        });

        // Update existing auctions with default values
        DB::table('auctions')->update([
            'description' => null,
            'auction_type' => 'online',
            'featured_image' => null
        ]);
    }

    public function down()
    {
        Schema::table('auctions', function (Blueprint $table) {
            // Remove new columns
            $table->dropColumn(['description', 'auction_type', 'featured_image']);
        });
    }
}
